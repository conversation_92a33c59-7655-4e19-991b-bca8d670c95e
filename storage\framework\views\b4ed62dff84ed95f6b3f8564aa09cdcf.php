<div>
    <!-- Delivery Calculator -->
    <div class="delivery-calculator">
        <div class="d-flex align-items-center mb-3">
            <i class="fas fa-calculator text-dark me-2"></i>
            <h5 class="mb-0">Delivery Calculator</h5>
        </div>

        <div class="row align-items-end">
            <div class="col-md-6">
                <label for="zipCode" class="form-label">Your Zip Code</label>
                <input type="text" class="form-control" id="zipCode" wire:model="zipCode"
                    placeholder="Enter your zip code">
            </div>
            <div class="col-md-6">
                <button class="btn btn-dark" wire:click="calculateDelivery">
                    Calculate Total Delivery
                </button>
            </div>
        </div>
        <small class="text-muted">Delivery costs are based on individual container rates</small>
    </div>

    <!-- Cart Items -->
    <!--[if BLOCK]><![endif]--><?php if(count($cart) > 0): ?>
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cart; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="cart-item p-4 mb-3">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <img src="<?php echo e($item['image'] ?? 'https://via.placeholder.com/100'); ?>" class="img-fluid rounded"
                            alt="<?php echo e($item['name']); ?>" style="width: 100px; height: 100px; object-fit: cover;">
                    </div>

                    <div class="col-md-4">
                        <h6 class="mb-1"><?php echo e($item['name']); ?></h6>
                        <p class="text-muted small mb-2">
                            <?php echo e($item['description'] ?? 'Perfect for residential storage needs or small business inventory.'); ?>

                        </p>

                        <div class="mb-2">
                            <span class="tag-pill"><?php echo e($item['size']); ?>ft</span>
                            <span class="tag-pill"><?php echo e(strtolower($item['category'] ?? 'storage')); ?></span>
                            <span class="tag-pill"><?php echo e($item['rentalMonths'] ?? 1); ?>mo</span>
                            <span class="tag-pill">Delivery: $<?php echo e(number_format($item['deliveryCost'] ?? 0, 2)); ?></span>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-dark me-2"><?php echo e($item['type']); ?></span>
                            <!--[if BLOCK]><![endif]--><?php if($item['type'] === 'Rental'): ?>
                                <span class="badge bg-secondary"><?php echo e($item['rentalMonths']); ?>

                                    month<?php echo e($item['rentalMonths'] > 1 ? 's' : ''); ?></span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!--[if BLOCK]><![endif]--><?php if($item['type'] === 'Rental'): ?>
                            <!-- Rental: Control months (1-12) -->
                            <div class="d-flex align-items-center gap-2" style="margin-top: 10px;">
                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateRentalMonths('<?php echo e($key); ?>', <?php echo e(max(1, ($item['rentalMonths'] ?? 1) - 1)); ?>)"
                                    <?php echo e(($item['rentalMonths'] ?? 1) <= 1 ? 'disabled' : ''); ?>>-</button>

                                <input type="text" min="1" max="12"
                                    wire:change="updateRentalMonths('<?php echo e($key); ?>', $event.target.value)"
                                    value="<?php echo e($item['rentalMonths'] ?? 1); ?>" class="form-control text-center"
                                    style="width: 70px; font-size: 12px;" />

                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateRentalMonths('<?php echo e($key); ?>', <?php echo e(min(12, ($item['rentalMonths'] ?? 1) + 1)); ?>)"
                                    <?php echo e(($item['rentalMonths'] ?? 1) >= 12 ? 'disabled' : ''); ?>>+</button>
                            </div>
                            <small class="text-muted">Months (1-12)</small>
                        <?php else: ?>
                            <!-- Purchase: Control quantity -->
                            <div class="d-flex align-items-center gap-2" style="margin-top: 10px;">
                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateQuantity('<?php echo e($key); ?>', <?php echo e(($item['qty'] ?? 1) - 1); ?>)">-</button>

                                <input type="text" min="1"
                                    wire:change="updateQuantity('<?php echo e($key); ?>', $event.target.value)"
                                    value="<?php echo e($item['qty'] ?? 1); ?>" class="form-control text-center"
                                    style="width: 70px; font-size: 12px;" />

                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateQuantity('<?php echo e($key); ?>', <?php echo e(($item['qty'] ?? 1) + 1); ?>)">+</button>
                            </div>
                            <small class="text-muted">Quantity</small>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    </div>

                    <div class="col-md-2 text-end">
                        <button class="btn btn-link text-danger p-0 mb-2"
                            wire:click="removeItem('<?php echo e($key); ?>')">
                            <i class="fas fa-trash"></i>
                        </button>


                        <div class="fw-bold">
                            <span id="ProductPrice" style="display: none;">
                                $<?php echo e(number_format($item['price'], 2)); ?>

                            </span>
                            <span id="ProductPriceTotal">$<?php echo e(number_format($item['price'] * $item['qty'], 2)); ?></span>
                        </div>
                        <small class="text-muted">
                            <?php echo e($item['type'] === 'Rental' ? 'Rental cost' : 'Purchase cost'); ?>

                        </small>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Your cart is empty</h5>
            <p class="text-muted">Add some containers to get started!</p>
            <a href="<?php echo e(route('catalog')); ?>" class="btn btn-primary">
                Continue Shopping
            </a>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <!-- JavaScript for alerts -->
    <script>
        document.addEventListener('livewire:load', function() {
            Livewire.on('show-alert', function(data) {
                // You can use any alert library here (Bootstrap, SweetAlert, etc.)
                alert(data.message);
            });

            // Debug: Log when delivery is calculated
            Livewire.on('delivery-calculated', function(data) {
                console.log('Delivery calculated:', data);
            });
        });
    </script>


    <script>
        // document.addEventListener('DOMContentLoaded', function() {
        //     // IDs for buttons and input
        //     const qtyMinus = document.getElementById('qtyMinus');
        //     const qtyPlus = document.getElementById('qtyPlus');
        //     const qtyInput = document.getElementById('qtyInput');

        //     // Order summary IDs
        //     const subtotalEl = document.getElementById('SubtotalSummary');
        //     const deliveryEl = document.getElementById('DeliverySummary');
        //     const taxEl = document.getElementById('TaxSummary');
        //     const totalEl = document.getElementById('TotalSummary');
        //     const ProductPriceTotal = document.getElementById("ProductPriceTotal");
        //     const TotalBtnSummary = document.getElementById("TotalBtnSummary");


        //     // Assuming a base price per unit
        //     const pricePerItem = parseFloat(document.getElementById("ProductPrice").innerHTML.replace(/[^0-9.]/g,
        //         ''));
        //     const deliveryCost = parseFloat(deliveryEl.innerHTML.replace(/[^0-9.]/g, ''));
        //     const taxRate = 0.13;
        //     // alert(pricePerItem); // 333

        //     function updateSummary() {
        //         const qty = parseInt(qtyInput.value) || 1;
        //         const subtotal = pricePerItem * qty;
        //         const tax = (subtotal + deliveryCost) * taxRate;
        //         const total = subtotal + deliveryCost + tax;

        //         subtotalEl.innerHTML = `$${subtotal.toFixed(2)}`;
        //         taxEl.innerHTML = `$${tax.toFixed(2)}`;
        //         totalEl.innerHTML = TotalBtnSummary.innerHTML = `$${total.toFixed(2)}`;
        //         ProductPriceTotal.innerHTML = `$${(pricePerItem * qty).toFixed(2)}`

        //         // Save values to localStorage
        //         localStorage.setItem('orderSubtotal', subtotal.toFixed(2));
        //         localStorage.setItem('orderDelivery', deliveryCost.toFixed(2));
        //         localStorage.setItem('orderTax', tax.toFixed(2));
        //         localStorage.setItem('orderTotal', total.toFixed(2));

        //     }

        //     qtyMinus.addEventListener('click', () => {
        //         let currentVal = parseInt(qtyInput.value) || 1;
        //         if (currentVal > 1) {
        //             qtyInput.value = currentVal - 1;
        //             updateSummary();
        //         }
        //     });

        //     qtyPlus.addEventListener('click', () => {
        //         let currentVal = parseInt(qtyInput.value) || 1;
        //         qtyInput.value = currentVal + 1;
        //         updateSummary();
        //     });

        //     qtyInput.addEventListener('input', () => {
        //         if (qtyInput.value < 1) qtyInput.value = 1;
        //         updateSummary();
        //     });
        // });
    </script>
</div>
<?php /**PATH C:\xampp\htdocs\containers\resources\views/livewire/cart-items.blade.php ENDPATH**/ ?>