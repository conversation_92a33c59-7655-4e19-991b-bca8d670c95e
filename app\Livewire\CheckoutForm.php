<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Customer;

class CheckoutForm extends Component
{
    // Personal Information
    public $firstName = '';
    public $lastName = '';
    public $email = '';
    public $phone = '';

    // Delivery Address
    public $streetAddress = '';
    public $city = '';
    public $province = '';
    public $zipCode = '';



    // Order Data
    public $cart = [];
    public $subtotal = 0;
    public $deliveryCost = 0;
    public $tax = 0;
    public $total = 0;
    public $agreeToTerms = false;

    protected $rules = [
        'firstName' => 'required|string|max:255',
        'lastName' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:20',
        'streetAddress' => 'required|string|max:255',
        'city' => 'required|string|max:255',
        'province' => 'required|string|max:255',
        'zipCode' => 'required|string|max:10',
    ];

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->subtotal = session('cart_subtotal', 0);
        $this->deliveryCost = session('delivery_cost', 0);
        $this->tax = session('cart_tax', 0);
        $this->total = $this->subtotal + $this->deliveryCost + $this->tax;
        // Clear any existing session data and initialize form fields as empty
        session()->forget('checkout_form');
        session()->forget('checkout_form_complete');
                // dd(session('zip_code'));
        $this->firstName = '';
        $this->lastName = '';
        $this->email = '';
        $this->phone = '';
        $this->streetAddress = '';
        $this->city = '';
        $this->province = '';
        $this->zipCode = '';
        // User must input their own zip code
        $this->zipCode = '';
    }



    public function updated($propertyName)
    {
        // Save form data to session as user types
        session(["checkout_form.{$propertyName}" => $this->$propertyName]);

        // Also save zip code to main session for consistency across components
        if ($propertyName === 'zipCode') {
            session(['zip_code' => $this->zipCode]);
        }
    }

    public function render()
    {
        return view('livewire.checkout-form');
    }
}
